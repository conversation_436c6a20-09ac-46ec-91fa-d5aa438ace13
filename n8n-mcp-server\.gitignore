# Build
build/
dist/
*.tsbuildinfo

# Dependencies
node_modules/
.pnp/
.pnp.js
.npm/

# Environment
.env
.env.local
.env.*.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# IDE
.idea/
.vscode/
*.swp
*.swo
.project
.classpath
.settings/
*.sublime-workspace
*.sublime-project

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# Testing
coverage/
.nyc_output/
junit.xml
*.lcov

# Temporary files
*.tmp
*.temp
.cache/
.temp/
tmp/

# TypeScript
*.js.map
