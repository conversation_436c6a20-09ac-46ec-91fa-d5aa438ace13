---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠==


# Text Elements
小黑子出击！ ^xhz001

鸡你太美！！！ ^jntm001

哈哈哈哈 ^haha001

我要打十个！ ^fight001

%%
## Drawing
```json
{
  "type": "excalidraw",
  "version": 2,
  "source": "https://marketplace.visualstudio.com/items?itemName=pomdtr.excalidraw-editor",
  "elements": [
    {
      "type": "ellipse",
      "version": 301,
      "versionNonce": 3001001001,
      "isDeleted": false,
      "id": "head001",
      "fillStyle": "solid",
      "strokeWidth": 4,
      "strokeStyle": "solid",
      "roughness": 2,
      "opacity": 100,
      "angle": 0.2,
      "x": 300,
      "y": 100,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "#ffec99",
      "width": 120,
      "height": 110,
      "seed": 301001,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "ellipse",
      "version": 302,
      "versionNonce": 3001001002,
      "isDeleted": false,
      "id": "glasses001",
      "fillStyle": "hachure",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 2,
      "opacity": 100,
      "angle": 0.1,
      "x": 310,
      "y": 125,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 30,
      "height": 25,
      "seed": 301002,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "ellipse",
      "version": 303,
      "versionNonce": 3001001003,
      "isDeleted": false,
      "id": "glasses002",
      "fillStyle": "hachure",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 2,
      "opacity": 100,
      "angle": -0.1,
      "x": 365,
      "y": 125,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 30,
      "height": 25,
      "seed": 301003,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "line",
      "version": 304,
      "versionNonce": 3001001004,
      "isDeleted": false,
      "id": "glassesbridge001",
      "fillStyle": "hachure",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 2,
      "opacity": 100,
      "angle": 0,
      "x": 340,
      "y": 137,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 25,
      "height": 0,
      "seed": 301004,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": null,
      "points": [
        [0, 0],
        [25, 0]
      ]
    },
    {
      "type": "ellipse",
      "version": 305,
      "versionNonce": 3001001005,
      "isDeleted": false,
      "id": "eye001",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 318,
      "y": 130,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "#000000",
      "width": 12,
      "height": 12,
      "seed": 301005,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "ellipse",
      "version": 306,
      "versionNonce": 3001001006,
      "isDeleted": false,
      "id": "eye002",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 373,
      "y": 130,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "#000000",
      "width": 12,
      "height": 12,
      "seed": 301006,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "ellipse",
      "version": 307,
      "versionNonce": 3001001007,
      "isDeleted": false,
      "id": "eyeshine001",
      "fillStyle": "solid",
      "strokeWidth": 1,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 322,
      "y": 132,
      "strokeColor": "#ffffff",
      "backgroundColor": "#ffffff",
      "width": 4,
      "height": 4,
      "seed": 301007,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "ellipse",
      "version": 308,
      "versionNonce": 3001001008,
      "isDeleted": false,
      "id": "eyeshine002",
      "fillStyle": "solid",
      "strokeWidth": 1,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 377,
      "y": 132,
      "strokeColor": "#ffffff",
      "backgroundColor": "#ffffff",
      "width": 4,
      "height": 4,
      "seed": 301008,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "ellipse",
      "version": 309,
      "versionNonce": 3001001009,
      "isDeleted": false,
      "id": "mouth001",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 2,
      "opacity": 100,
      "angle": 0,
      "x": 330,
      "y": 165,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "#e03131",
      "width": 50,
      "height": 30,
      "seed": 301009,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "ellipse",
      "version": 310,
      "versionNonce": 3001001010,
      "isDeleted": false,
      "id": "teeth001",
      "fillStyle": "solid",
      "strokeWidth": 1,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 340,
      "y": 172,
      "strokeColor": "#ffffff",
      "backgroundColor": "#ffffff",
      "width": 8,
      "height": 12,
      "seed": 301010,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "ellipse",
      "version": 311,
      "versionNonce": 3001001011,
      "isDeleted": false,
      "id": "teeth002",
      "fillStyle": "solid",
      "strokeWidth": 1,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 350,
      "y": 172,
      "strokeColor": "#ffffff",
      "backgroundColor": "#ffffff",
      "width": 8,
      "height": 12,
      "seed": 301011,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "ellipse",
      "version": 312,
      "versionNonce": 3001001012,
      "isDeleted": false,
      "id": "teeth003",
      "fillStyle": "solid",
      "strokeWidth": 1,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 360,
      "y": 172,
      "strokeColor": "#ffffff",
      "backgroundColor": "#ffffff",
      "width": 8,
      "height": 12,
      "seed": 301012,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "ellipse",
      "version": 313,
      "versionNonce": 3001001013,
      "isDeleted": false,
      "id": "hood001",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 2,
      "opacity": 100,
      "angle": 0.3,
      "x": 280,
      "y": 70,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "#2d2d2d",
      "width": 140,
      "height": 80,
      "seed": 301013,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "rectangle",
      "version": 314,
      "versionNonce": 3001001014,
      "isDeleted": false,
      "id": "hoodie001",
      "fillStyle": "solid",
      "strokeWidth": 4,
      "strokeStyle": "solid",
      "roughness": 2,
      "opacity": 100,
      "angle": 0.1,
      "x": 270,
      "y": 210,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "#2d2d2d",
      "width": 160,
      "height": 130,
      "seed": 301014,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "rectangle",
      "version": 315,
      "versionNonce": 3001001015,
      "isDeleted": false,
      "id": "pants001",
      "fillStyle": "solid",
      "strokeWidth": 4,
      "strokeStyle": "solid",
      "roughness": 2,
      "opacity": 100,
      "angle": -0.1,
      "x": 290,
      "y": 340,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "#868e96",
      "width": 120,
      "height": 160,
      "seed": 301015,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "ellipse",
      "version": 316,
      "versionNonce": 3001001016,
      "isDeleted": false,
      "id": "shoe001",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 2,
      "opacity": 100,
      "angle": 0.2,
      "x": 280,
      "y": 500,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "#1e1e1e",
      "width": 60,
      "height": 30,
      "seed": 301016,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "ellipse",
      "version": 317,
      "versionNonce": 3001001017,
      "isDeleted": false,
      "id": "shoe002",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 2,
      "opacity": 100,
      "angle": -0.2,
      "x": 360,
      "y": 500,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "#1e1e1e",
      "width": 60,
      "height": 30,
      "seed": 301017,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "ellipse",
      "version": 318,
      "versionNonce": 3001001018,
      "isDeleted": false,
      "id": "basketball001",
      "fillStyle": "solid",
      "strokeWidth": 4,
      "strokeStyle": "solid",
      "roughness": 2,
      "opacity": 100,
      "angle": 0.3,
      "x": 150,
      "y": 250,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "#fd7e14",
      "width": 80,
      "height": 80,
      "seed": 301018,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "line",
      "version": 319,
      "versionNonce": 3001001019,
      "isDeleted": false,
      "id": "basketballline001",
      "fillStyle": "hachure",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 2,
      "opacity": 100,
      "angle": 0,
      "x": 150,
      "y": 290,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 80,
      "height": 0,
      "seed": 301019,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": null,
      "points": [
        [0, 0],
        [80, 0]
      ]
    },
    {
      "type": "line",
      "version": 320,
      "versionNonce": 3001001020,
      "isDeleted": false,
      "id": "basketballline002",
      "fillStyle": "hachure",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 2,
      "opacity": 100,
      "angle": 0,
      "x": 190,
      "y": 250,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 80,
      "seed": 301020,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": null,
      "points": [
        [0, 0],
        [0, 80]
      ]
    },
    {
      "type": "line",
      "version": 321,
      "versionNonce": 3001001021,
      "isDeleted": false,
      "id": "arm001",
      "fillStyle": "hachure",
      "strokeWidth": 12,
      "strokeStyle": "solid",
      "roughness": 2,
      "opacity": 100,
      "angle": 0,
      "x": 270,
      "y": 250,
      "strokeColor": "#2d2d2d",
      "backgroundColor": "transparent",
      "width": 80,
      "height": 80,
      "seed": 301021,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": null,
      "points": [
        [0, 0],
        [-80, 80]
      ]
    },
    {
      "type": "line",
      "version": 322,
      "versionNonce": 3001001022,
      "isDeleted": false,
      "id": "arm002",
      "fillStyle": "hachure",
      "strokeWidth": 12,
      "strokeStyle": "solid",
      "roughness": 2,
      "opacity": 100,
      "angle": 0,
      "x": 430,
      "y": 250,
      "strokeColor": "#2d2d2d",
      "backgroundColor": "transparent",
      "width": 60,
      "height": 100,
      "seed": 301022,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": null,
      "points": [
        [0, 0],
        [60, 100]
      ]
    },
    {
      "type": "text",
      "version": 323,
      "versionNonce": 3001001023,
      "isDeleted": false,
      "id": "xhz001",
      "fillStyle": "hachure",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 2,
      "opacity": 100,
      "angle": -0.1,
      "x": 300,
      "y": 20,
      "strokeColor": "#e03131",
      "backgroundColor": "transparent",
      "width": 120,
      "height": 35,
      "seed": 301023,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 28,
      "fontFamily": 1,
      "text": "小黑子出击！",
      "textAlign": "center",
      "verticalAlign": "top",
      "containerId": null,
      "originalText": "小黑子出击！",
      "lineHeight": 1.25,
      "baseline": 25
    },
    {
      "type": "text",
      "version": 324,
      "versionNonce": 3001001024,
      "isDeleted": false,
      "id": "jntm001",
      "fillStyle": "hachure",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 2,
      "opacity": 100,
      "angle": 0.1,
      "x": 280,
      "y": 550,
      "strokeColor": "#fd7e14",
      "backgroundColor": "transparent",
      "width": 140,
      "height": 35,
      "seed": 301024,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 24,
      "fontFamily": 1,
      "text": "鸡你太美！！！",
      "textAlign": "center",
      "verticalAlign": "top",
      "containerId": null,
      "originalText": "鸡你太美！！！",
      "lineHeight": 1.25,
      "baseline": 25
    },
    {
      "type": "text",
      "version": 325,
      "versionNonce": 3001001025,
      "isDeleted": false,
      "id": "haha001",
      "fillStyle": "hachure",
      "strokeWidth": 1,
      "strokeStyle": "solid",
      "roughness": 2,
      "opacity": 80,
      "angle": 0.2,
      "x": 450,
      "y": 120,
      "strokeColor": "#495057",
      "backgroundColor": "transparent",
      "width": 80,
      "height": 30,
      "seed": 301025,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 20,
      "fontFamily": 1,
      "text": "哈哈哈哈",
      "textAlign": "center",
      "verticalAlign": "top",
      "containerId": null,
      "originalText": "哈哈哈哈",
      "lineHeight": 1.25,
      "baseline": 21
    },
    {
      "type": "text",
      "version": 326,
      "versionNonce": 3001001026,
      "isDeleted": false,
      "id": "fight001",
      "fillStyle": "hachure",
      "strokeWidth": 1,
      "strokeStyle": "solid",
      "roughness": 2,
      "opacity": 90,
      "angle": -0.15,
      "x": 500,
      "y": 350,
      "strokeColor": "#1971c2",
      "backgroundColor": "transparent",
      "width": 100,
      "height": 30,
      "seed": 301026,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 1,
      "text": "我要打十个！",
      "textAlign": "center",
      "verticalAlign": "top",
      "containerId": null,
      "originalText": "我要打十个！",
      "lineHeight": 1.25,
      "baseline": 21
    },
    {
      "type": "line",
      "version": 327,
      "versionNonce": 3001001027,
      "isDeleted": false,
      "id": "motion001",
      "fillStyle": "hachure",
      "strokeWidth": 3,
      "strokeStyle": "dashed",
      "roughness": 2,
      "opacity": 70,
      "angle": 0,
      "x": 100,
      "y": 280,
      "strokeColor": "#fd7e14",
      "backgroundColor": "transparent",
      "width": 50,
      "height": 30,
      "seed": 301027,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": null,
      "points": [
        [0, 0],
        [25, -30],
        [50, 0]
      ]
    },
    {
      "type": "line",
      "version": 328,
      "versionNonce": 3001001028,
      "isDeleted": false,
      "id": "motion002",
      "fillStyle": "hachure",
      "strokeWidth": 2,
      "strokeStyle": "dashed",
      "roughness": 2,
      "opacity": 60,
      "angle": 0,
      "x": 80,
      "y": 320,
      "strokeColor": "#fd7e14",
      "backgroundColor": "transparent",
      "width": 40,
      "height": 20,
      "seed": 301028,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": null,
      "points": [
        [0, 0],
        [20, -20],
        [40, 0]
      ]
    },
    {
      "type": "ellipse",
      "version": 329,
      "versionNonce": 3001001029,
      "isDeleted": false,
      "id": "sweat001",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 80,
      "angle": 0,
      "x": 390,
      "y": 110,
      "strokeColor": "#339af0",
      "backgroundColor": "#74c0fc",
      "width": 8,
      "height": 12,
      "seed": 301029,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "ellipse",
      "version": 330,
      "versionNonce": 3001001030,
      "isDeleted": false,
      "id": "sweat002",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 80,
      "angle": 0,
      "x": 405,
      "y": 115,
      "strokeColor": "#339af0",
      "backgroundColor": "#74c0fc",
      "width": 6,
      "height": 10,
      "seed": 301030,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    }
  ],
  "appState": {
    "gridSize": null,
    "viewBackgroundColor": "#ffffff"
  },
  "files": {}
}
```
%%
